# Debugging Assessment Display Issue

## Masalah
Assessment sudah disubmit (token terpakai) tapi tidak muncul di tabel dashboard, kemungkinan karena data `persona_profile.archetype` belum terisi.

## Debugging yang Ditambahkan

### 1. Di `src/hooks/useDashboard.js`
Menambahkan console.log untuk melihat struktur data yang dikembalikan dari API:
```javascript
console.log('Dashboard Results:', {
  total_results: response.data.results?.length || 0,
  results_summary: response.data.results?.map(r => ({
    id: r.id,
    status: r.status,
    has_persona_profile: !!r.persona_profile,
    archetype: r.persona_profile?.archetype || 'none'
  })) || []
});
```

### 2. Di `src/components/Dashboard/components/ResultsTable.jsx`
Memperbaiki kondisi rendering untuk menampilkan semua assessment, bukan hanya yang memiliki archetype:

**Sebelum:**
```javascript
{result.status === 'completed' && result.persona_profile?.archetype ? (
  // Show archetype
) : (
  // Show "No archetype" or "Processing..."
)}
```

**Sesudah:**
```javascript
{result.status === 'completed' ? (
  result.persona_profile?.archetype ? (
    // Show archetype
  ) : (
    // Show "No archetype"
  )
) : result.status === 'processing' ? (
  // Show "Processing..."
) : result.status === 'failed' ? (
  // Show "Failed"
) : (
  // Show "Unknown"
)}
```

## Cara Debugging

1. **Buka Developer Console** di browser (F12)
2. **Login ke aplikasi** dan buka dashboard
3. **Lihat console output** untuk melihat struktur data yang dikembalikan
4. **Cari log "Dashboard Results:"** untuk melihat summary assessment

## Kemungkinan Penyebab & Solusi

### 1. Assessment ada tapi persona_profile kosong
**Gejala:** Console menunjukkan `has_persona_profile: false` atau `archetype: 'none'`
**Solusi:** Periksa backend processing, kemungkinan AI analysis belum selesai

### 2. Assessment tidak dikembalikan oleh API
**Gejala:** Console menunjukkan `total_results: 0`
**Solusi:** Periksa API endpoint `/api/archive/results` dan database

### 3. Status assessment masih 'processing'
**Gejala:** Console menunjukkan `status: 'processing'`
**Solusi:** Tunggu processing selesai atau periksa background job

### 4. Assessment gagal diproses
**Gejala:** Console menunjukkan `status: 'failed'`
**Solusi:** Periksa error log di backend

## Query Database untuk Debugging

Untuk memeriksa data di database secara langsung:

```sql
-- Lihat semua assessment user
SELECT 
  id, 
  user_id, 
  status, 
  assessment_name,
  created_at,
  updated_at,
  CASE 
    WHEN persona_profile IS NULL THEN 'NULL'
    WHEN JSON_EXTRACT(persona_profile, '$.archetype') IS NULL THEN 'NO_ARCHETYPE'
    ELSE JSON_EXTRACT(persona_profile, '$.archetype')
  END as archetype_status
FROM assessment_results 
WHERE user_id = [USER_ID]
ORDER BY created_at DESC;

-- Lihat detail persona_profile
SELECT 
  id,
  status,
  persona_profile,
  JSON_EXTRACT(persona_profile, '$.archetype') as archetype
FROM assessment_results 
WHERE user_id = [USER_ID] 
  AND status = 'completed'
ORDER BY created_at DESC;
```

## Langkah Selanjutnya

1. **Jalankan debugging** dan lihat console output
2. **Identifikasi masalah** berdasarkan output console
3. **Periksa database** jika diperlukan
4. **Perbaiki backend processing** jika assessment tidak diproses dengan benar
5. **Update frontend** jika ada masalah dengan kondisi rendering

## Catatan Penting

- Perubahan ini akan menampilkan **semua assessment** di tabel, termasuk yang belum memiliki archetype
- Assessment dengan status 'completed' tapi tanpa archetype akan menampilkan "No archetype"
- Assessment dengan status 'processing' akan menampilkan "Processing..."
- Assessment dengan status 'failed' akan menampilkan "Failed"
